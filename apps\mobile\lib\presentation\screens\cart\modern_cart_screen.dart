import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_strings.dart';
import '../../../core/constants/app_colors.dart';
import '../../../theme/app_theme.dart';

/// Modern Cart Screen - UI-only implementation matching the provided screenshot design
/// This screen follows the clean architecture pattern and Dayliz design system
///
/// Features:
/// - Delivery time display
/// - Product cards with quantity controls
/// - Coupon section with applied discounts
/// - Detailed price breakdown
/// - Address selection
/// - Modern, clean UI design
class ModernCartScreen extends ConsumerStatefulWidget {
  const ModernCartScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ModernCartScreen> createState() => _ModernCartScreenState();
}

class _ModernCartScreenState extends ConsumerState<ModernCartScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final daylizTheme = theme.extension<DaylizThemeExtension>();

    // Watch cart state (placeholder for now)
    // final cartState = ref.watch(cartNotifierProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(context, theme),
      body: _buildBody(context, theme, daylizTheme),
      bottomNavigationBar: _buildBottomSection(context, theme, daylizTheme),
    );
  }

  /// Builds the app bar with back button and title
  PreferredSizeWidget _buildAppBar(BuildContext context, ThemeData theme) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text(
        AppStrings.cart, // Use consistent app strings
        style: TextStyle(
          color: AppColors.textPrimary,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: false,
    );
  }

  /// Builds the main body content
  Widget _buildBody(BuildContext context, ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDeliveryTimeSection(theme, daylizTheme),
          const SizedBox(height: 2),
          _buildCartItems(theme, daylizTheme),
          const SizedBox(height: 35), // Increased from 16 to 24
          _buildCouponSection(theme, daylizTheme),
          const SizedBox(height: 35),
          _buildPriceBreakdown(theme, daylizTheme),
          const SizedBox(height: 35),
          _buildCancellationPolicy(theme, daylizTheme),
          const SizedBox(height: 20), // Reduced space for bottom section
        ],
      ),
    );
  }

  /// Builds the delivery time section
  Widget _buildDeliveryTimeSection(ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
          bottomLeft: Radius.circular(0),
          bottomRight: Radius.circular(0),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.access_time,
              color: AppColors.success,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Delivery in 12 minutes',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                'Shipment of 2 items',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the cart items section
  Widget _buildCartItems(ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(0),
          topRight: Radius.circular(0),
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        children: [
          _buildCartItem(
            imageUrl: 'https://via.placeholder.com/60',
            title: 'Parle Center Filled Biscuits (Choco, Milano)',
            weight: '250 g',
            originalPrice: '₹170',
            discountedPrice: '₹88',
            quantity: 2,
            theme: theme,
            daylizTheme: daylizTheme,
          ),
          const SizedBox(height: 16),
          _buildCartItem(
            imageUrl: 'https://via.placeholder.com/60',
            title: 'Aashirvaad Superior MP Whole Wheat Atta',
            weight: '1 kg',
            originalPrice: '₹70',
            discountedPrice: '₹70',
            quantity: 1,
            theme: theme,
            daylizTheme: daylizTheme,
          ),
        ],
      ),
    );
  }

  /// Builds individual cart item
  Widget _buildCartItem({
    required String imageUrl,
    required String title,
    required String weight,
    required String originalPrice,
    required String discountedPrice,
    required int quantity,
    required ThemeData theme,
    required DaylizThemeExtension? daylizTheme,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product Image
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[100],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[200],
                  child: const Icon(Icons.image, color: Colors.grey),
                );
              },
            ),
          ),
        ),
        const SizedBox(width: 12),

        // Product Details and Controls
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Name and Quantity/Price Controls Row
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Name (takes remaining space)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          weight,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),

                  // Quantity Controls and Price Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Quantity Controls (compact size)
                      _buildQuantityControls(quantity, theme),
                      const SizedBox(height: 4),

                      // Price Section (directly below add button)
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (originalPrice != discountedPrice) ...[
                            Text(
                              originalPrice,
                              style: const TextStyle(
                                fontSize: 13,
                                color: AppColors.textSecondary,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                            const SizedBox(width: 4),
                          ],
                          Text(
                            discountedPrice,
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds quantity control buttons (compact size)
  Widget _buildQuantityControls(int quantity, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.success),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildQuantityButton(
            icon: Icons.remove,
            onTap: () {
              // TODO: Implement decrease quantity
            },
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 7),
            child: Text(
              quantity.toString(),
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: AppColors.success,
              ),
            ),
          ),
          _buildQuantityButton(
            icon: Icons.add,
            onTap: () {
              // TODO: Implement increase quantity
            },
          ),
        ],
      ),
    );
  }

  /// Builds individual quantity button (compact size)
  Widget _buildQuantityButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final isAdd = icon == Icons.add;
    return Semantics(
      label: isAdd ? 'Increase quantity' : 'Decrease quantity',
      button: true,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          width: 26, // Fixed width for consistency
          height: 26, // Fixed height for consistency
          alignment: Alignment.center,
          child: Icon(
            icon,
            size: 13,
            color: AppColors.success,
          ),
        ),
      ),
    );
  }

  /// Builds the coupon section
  Widget _buildCouponSection(ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return Container(
      padding: const EdgeInsets.all(8), // Reduced from EdgeInsets.fromLTRB(4, 4, 4, 2)
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Free Delivery Section with light blue background
          Container(
            padding: const EdgeInsets.all(10), // Reduced from 12
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6), // Reduced from 8
                  decoration: BoxDecoration(
                    color: AppColors.info.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6), // Reduced from 8
                  ),
                  child: const Icon(
                    Icons.two_wheeler,
                    color: AppColors.info,
                    size: 18, // Reduced from 20
                  ),
                ),
                const SizedBox(width: 10), // Reduced from 12
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Get FREE delivery',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.info,
                        ),
                      ),
                      const SizedBox(height: 3), // Reduced from 4
                      const Row(
                        children: [
                          Text(
                            'Add products worth ₹53 more',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(width: 4),
                          Icon(
                            Icons.arrow_forward,
                            size: 14,
                            color: AppColors.textSecondary,
                          ),
                        ],
                      ),
                      const SizedBox(height: 6), // Reduced from 8
                      // Progress Bar
                      Container(
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: 0.7, // 70% progress (adjust as needed)
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.info,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 6), // Reduced from 8

          // See All Coupons Button
          Padding(
            padding: const EdgeInsets.fromLTRB(4, 2, 4, 0), // Reduced padding
            child: Center(
              child: TextButton(
                style: TextButton.styleFrom(
                  minimumSize: const Size(0, 28), // Reduced button height from 32
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2), // Reduced padding
                ),
                onPressed: () {
                  // TODO: Implement see all coupons
                },
                child: const Text(
                  'See all coupons ▶',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the price breakdown section
  Widget _buildPriceBreakdown(ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildPriceRow('Item total', '₹336'),
          const SizedBox(height: 8),
          _buildPriceRow('Taxes and Charges', '₹6.7'),
          const SizedBox(height: 8),
          _buildPriceRow('Delivery Fee', '₹20.2'),
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),
          _buildPriceRow(
            'Grand Total',
            '₹362.9',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// Builds individual price row
  Widget _buildPriceRow(String label, String amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  /// Builds the cancellation policy section
  Widget _buildCancellationPolicy(ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cancellation Policy',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Orders cannot be cancelled once packed for delivery. In case of unexpected delays, a refund will be provided, if applicable.',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the bottom section with address and place order button
  Widget _buildBottomSection(BuildContext context, ThemeData theme, DaylizThemeExtension? daylizTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Address Section
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.home,
                  color: AppColors.success,
                  size: 14,
                ),
              ),
              const SizedBox(width: 6),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivering to Home',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      '1, 1',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              TextButton(
                style: TextButton.styleFrom(
                  minimumSize: const Size(0, 24),
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                ),
                onPressed: () {
                  // TODO: Implement change address
                },
                child: const Text(
                  'Change',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.success,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Thin divider
          Container(
            height: 1,
            color: Colors.grey[200],
          ),
          const SizedBox(height: 8), // Reduced from 16 to 8

          // Payment and Place Order Section
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Payment Method with Card Icon
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Icon and PAY USING in same row
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Icon(
                            Icons.credit_card,
                            color: AppColors.textSecondary,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'PAY USING',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.keyboard_arrow_up,
                          color: AppColors.textSecondary,
                          size: 16,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Cash on Delivery below both icon and PAY USING
                    const Text(
                      'Cash on Delivery',
                      style: TextStyle(
                        fontSize: 12, // Changed from 14 to 12 to match PAY USING
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Place Order Button with rounded corners (not sides)
              Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16), // Reduced padding
                decoration: BoxDecoration(
                  color: AppColors.success,
                  borderRadius: BorderRadius.circular(12), // Rounded corners, not sides
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      // TODO: Implement place order
                      _showPlaceOrderDialog(context);
                    },
                    child: const Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '₹262.8',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14, // Reduced from 16 to 14
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                'TOTAL',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 9, // Reduced from 10 to 9
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(width: 10), // Reduced spacing
                          Text(
                            'Place order',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Shows place order confirmation dialog
  void _showPlaceOrderDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Place Order'),
          content: const Text('This is a UI-only implementation. Order functionality will be added later.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
